from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        -- Drop the deprecated cert_order_shipping_relate table
        DROP TABLE IF EXISTS "cert_order_shipping_relate";
        """


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        -- Recreate the cert_order_shipping_relate table if needed for rollback
        CREATE TABLE IF NOT EXISTS "cert_order_shipping_relate" (
            "id" BIGSERIAL NOT NULL PRIMARY KEY,
            "create_user_id" INT,
            "update_user_id" INT,
            "is_deleted" BOOL NOT NULL DEFAULT False,
            "create_time" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
            "update_time" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
            "bind_cert_status" INT NOT NULL DEFAULT 1,
            "order_id" VARCHAR(128) NOT NULL,
            "shipping_order_id" VARCHAR(128) NOT NULL,
            "sku_id" VARCHAR(128) NOT NULL,
            "product_name" VARCHAR(256),
            "product_image" VARCHAR(256),
            "count" INT NOT NULL DEFAULT 1,
            "cert_sn_code" VARCHAR(128),
            "product_sn_code" VARCHAR(128),
            "bind_cert_time" TIMESTAMPTZ,
            "company_id" BIGINT NOT NULL,
            "inbound_order_id" BIGINT NOT NULL,
            "inbound_order_detail_id" BIGINT NOT NULL,
            "shipping_user_id" BIGINT NOT NULL,
            "waybill_code_id" BIGINT NOT NULL
        );
        CREATE INDEX IF NOT EXISTS "idx_cert_order__order_i_8744e3" ON "cert_order_shipping_relate" ("order_id");
        CREATE INDEX IF NOT EXISTS "idx_cert_order__shippin_ae6087" ON "cert_order_shipping_relate" ("shipping_order_id");
        CREATE INDEX IF NOT EXISTS "idx_cert_order__sku_id_f45513" ON "cert_order_shipping_relate" ("sku_id");
        CREATE INDEX IF NOT EXISTS "idx_cert_order__cert_sn_e20486" ON "cert_order_shipping_relate" ("cert_sn_code");
        CREATE INDEX IF NOT EXISTS "idx_cert_order__product_4554f7" ON "cert_order_shipping_relate" ("product_sn_code");
        COMMENT ON COLUMN "cert_order_shipping_relate"."id" IS '自增主键';
        COMMENT ON COLUMN "cert_order_shipping_relate"."create_user_id" IS '创建人ID';
        COMMENT ON COLUMN "cert_order_shipping_relate"."update_user_id" IS '更新人ID';
        COMMENT ON COLUMN "cert_order_shipping_relate"."is_deleted" IS '是否删除';
        COMMENT ON COLUMN "cert_order_shipping_relate"."create_time" IS '创建时间';
        COMMENT ON COLUMN "cert_order_shipping_relate"."update_time" IS '更新时间';
        COMMENT ON COLUMN "cert_order_shipping_relate"."bind_cert_status" IS '绑定证书状态(1:未绑定, 2:已绑定)';
        COMMENT ON COLUMN "cert_order_shipping_relate"."order_id" IS '订单号';
        COMMENT ON COLUMN "cert_order_shipping_relate"."shipping_order_id" IS '发货单号';
        COMMENT ON COLUMN "cert_order_shipping_relate"."sku_id" IS '商品SKU_ID';
        COMMENT ON COLUMN "cert_order_shipping_relate"."product_name" IS '商品名称';
        COMMENT ON COLUMN "cert_order_shipping_relate"."product_image" IS '商品图片';
        COMMENT ON COLUMN "cert_order_shipping_relate"."count" IS '数量';
        COMMENT ON COLUMN "cert_order_shipping_relate"."cert_sn_code" IS '证书SN码';
        COMMENT ON COLUMN "cert_order_shipping_relate"."product_sn_code" IS '商品SN码';
        COMMENT ON COLUMN "cert_order_shipping_relate"."bind_cert_time" IS '绑定证书时间';
        COMMENT ON COLUMN "cert_order_shipping_relate"."company_id" IS '公司';
        COMMENT ON COLUMN "cert_order_shipping_relate"."inbound_order_id" IS '入库单';
        COMMENT ON COLUMN "cert_order_shipping_relate"."inbound_order_detail_id" IS '入库单明细';
        COMMENT ON COLUMN "cert_order_shipping_relate"."shipping_user_id" IS '发货人';
        COMMENT ON COLUMN "cert_order_shipping_relate"."waybill_code_id" IS '发货单号';
        COMMENT ON TABLE "cert_order_shipping_relate" IS '订单与发货单/商品的关联表';
        """
