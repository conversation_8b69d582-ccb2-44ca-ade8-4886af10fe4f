# -*- coding: utf-8 -*-
from enum import Enum

from tortoise import fields
from tortoise.functions import Sum
from tortoise.transactions import in_transaction

from app.models.base import BasicFieldsModel


class InboundOrderStatusEnum(int, Enum):
    PENDING_SCAN = 1
    PENDING_SHIP = 2
    SHIPPING = 3
    COMPLETED = 4


class ShippingTypeEnum(int, Enum):
    SINGLE = 1
    DOUBLE = 2
    TRIPLE = 3
    MULTIPLE = 4


class CertTypeEnum(int, Enum):
    SINGLE = 1
    RANDOM = 2
    NO_CERT = 3


class WayBillCodeBindCertStatusEnum(int, Enum):
    UNBIND = 1
    BIND = 2


class InboundOrder(BasicFieldsModel):
    code = fields.CharField(max_length=32, description="入库单号", db_index=True)
    check_company_name = fields.CharField(max_length=64, description="公司名称")
    check_user = fields.ForeignKeyField("models.User", description="清点人员", related_name="inbound_orders")
    product_count = fields.IntField(description="商品数量", default=1)
    cert_count = fields.IntField(description="证书数量", default=1)
    waybill_count = fields.IntField(description="面单数量", default=1)
    status = fields.IntField(description="状态(1:待扫描, 2:待发货, 3:发货中, 4:已完成)", default=InboundOrderStatusEnum.PENDING_SCAN.value)
    company_id: int
    company = fields.ForeignKeyField(
        "models.Company",
        description="公司",
        related_name="inbound_orders",
        on_delete=fields.CASCADE,
        db_constraint=False,
    )

    class Meta:
        table = "cert_inbound_order"
        table_description = "入库单"
        unique_together = ("code", "company")

    async def save(self, *args, **kwargs):
        async with in_transaction("default"):
            if not self.code:
                self.code = await self.generate_sequence_code(self.company_id, prefix="RK", length=3)

            return await super().save(*args, **kwargs)

    @property
    async def current_status(self):
        """
        当前状态，根据inbound_order_detail进行判断
        待扫描：没有任务
        待发货：有任务单，但是没有发货人
        发货中：任意任务单被领取（有发货人）
        已完成：全部任务单都被领取（都有发货人）
        """
        details = await self.details_set.filter(is_deleted=False).all()
        # 总的明细单量
        total_batch_count = sum([detail.batch_count for detail in details])

        if not details:
            return InboundOrderStatusEnum.PENDING_SCAN.value

        if all([detail.shipping_user_id is not None for detail in details]) and total_batch_count == self.waybill_count:
            return InboundOrderStatusEnum.COMPLETED.value

        if any([detail.shipping_user_id is not None for detail in details]):
            return InboundOrderStatusEnum.SHIPPING.value

        return InboundOrderStatusEnum.PENDING_SHIP.value

    @property
    async def current_remaining_waybill_count(self) -> int:
        """面单余量

        Returns:
            int: 面单余量
        """

        details = await self.details_set.filter(is_deleted=False).annotate(total_batch_count=Sum("batch_count")).values("total_batch_count")
        total = details[0].get("total_batch_count") or 0 if details else 0
        return self.waybill_count - total


class InboundOrderDetail(BasicFieldsModel):
    inbound_order = fields.ForeignKeyField(
        "models.InboundOrder",
        description="入库单",
        on_delete=fields.CASCADE,
        db_constraint=False,
        related_name="details_set",
    )
    code = fields.CharField(max_length=32, description="任务单号", db_index=True)
    # 发货类型 1一单一件 2一单两件 3一单三件 4一单多件
    shipping_type = fields.IntField(description="发货类型(1:一单一件, 2:一单两件, 3:一单三件, 4:一单多件)", default=ShippingTypeEnum.SINGLE.value)
    # 发证方式 1一物一证 2随机发证 3不发证书
    cert_type = fields.IntField(description="发证方式(1:一物一证, 2:随机发证, 3:不发证书)", default=CertTypeEnum.SINGLE.value)
    # 发货人
    shipping_user = fields.ForeignKeyField(
        "models.User",
        description="发货人",
        on_delete=fields.CASCADE,
        db_constraint=False,
        null=True,
    )
    # 每批数量/面单数量
    batch_count = fields.IntField(description="每批数量", default=1)
    # 商品数量
    product_count = fields.IntField(description="商品数量", default=1)
    # 证书数量
    cert_count = fields.IntField(description="证书数量", default=1)
    company_id: int
    company = fields.ForeignKeyField(
        "models.Company",
        description="公司",
        on_delete=fields.CASCADE,
        db_constraint=False,
    )

    class Meta:
        table = "cert_inbound_order_detail"
        table_description = "入库单明细"
        unique_together = ("code", "company")

    async def save(self, *args, **kwargs):
        async with in_transaction("default"):
            if not self.code:
                self.code = await self.generate_sequence_code(self.company_id, prefix="QD", length=5)

            return await super().save(*args, **kwargs)


class ShippingOrderItem(BasicFieldsModel):
    """
    发货订单项目统一模型 - 合并原 WayBillCode 和 OrderShippingRelate 的功能
    通过单一模型实现快速查询：快递单号、订单号、证书编号
    """

    # === 基础标识信息 ===
    waybill_code = fields.CharField(max_length=32, description="快递单号/面单编号", db_index=True)
    order_id = fields.CharField(max_length=128, description="线上订单号", db_index=True)
    sku_id = fields.CharField(max_length=128, description="商品SKU_ID", db_index=True)

    # === 关联信息 ===
    inbound_order = fields.ForeignKeyField(
        "models.InboundOrder",
        description="入库单",
        on_delete=fields.CASCADE,
        db_constraint=False,
    )
    inbound_order_detail = fields.ForeignKeyField(
        "models.InboundOrderDetail",
        description="入库单明细",
        on_delete=fields.CASCADE,
        db_constraint=False,
        related_name="shipping_items_set",
    )
    company = fields.ForeignKeyField(
        "models.Company",
        description="公司",
        on_delete=fields.CASCADE,
        db_constraint=False,
    )
    shipping_user = fields.ForeignKeyField(
        "models.User",
        description="发货人",
        on_delete=fields.CASCADE,
        db_constraint=False,
        null=True,
    )

    # === 商品信息 ===
    product_name = fields.CharField(max_length=256, description="商品名称", null=True)
    product_image = fields.CharField(max_length=512, description="商品图片URL", null=True)
    count = fields.IntField(description="商品数量", default=1)

    # === 证书信息 ===
    cert_sn_code = fields.CharField(max_length=128, description="证书SN码", db_index=True, null=True)
    product_sn_code = fields.CharField(max_length=128, description="商品SN码", db_index=True, null=True)
    bind_cert_status = fields.IntField(
        description="绑定证书状态(1:未绑定, 2:已绑定)",
        default=WayBillCodeBindCertStatusEnum.UNBIND.value,
        db_index=True,
    )
    bind_cert_time = fields.DatetimeField(description="绑定证书时间", null=True)

    # === 冗余字段用于快速查询 ===
    inbound_order_code = fields.CharField(max_length=32, description="入库单号(冗余)", db_index=True, null=True)
    inbound_order_detail_code = fields.CharField(max_length=32, description="入库单明细号(冗余)", db_index=True, null=True)
    shipping_user_name = fields.CharField(max_length=64, description="发货人姓名(冗余)", null=True)

    class Meta:
        table = "cert_shipping_order_item"
        table_description = "发货订单项目统一表"
        # 复合索引优化查询性能
        indexes = [
            # 快递单号查询
            ("waybill_code", "company_id"),
            # 订单号查询
            ("order_id", "company_id"),
            # 证书查询
            ("cert_sn_code", "company_id"),
            ("product_sn_code", "company_id"),
            # 发货人查询
            ("shipping_user_id", "bind_cert_status"),
            # 入库单查询
            ("inbound_order_code", "company_id"),
        ]

    async def save(self, *args, **kwargs):
        """保存时自动填充冗余字段"""
        async with in_transaction("default"):
            # 自动填充冗余字段
            if self.inbound_order_id and not self.inbound_order_code:
                inbound_order = await self.inbound_order
                self.inbound_order_code = inbound_order.code

            if self.inbound_order_detail_id and not self.inbound_order_detail_code:
                inbound_order_detail = await self.inbound_order_detail
                self.inbound_order_detail_code = inbound_order_detail.code

            if self.shipping_user_id and not self.shipping_user_name:
                shipping_user = await self.shipping_user
                self.shipping_user_name = shipping_user.username

            return await super().save(*args, **kwargs)


# 保留原 WayBillCode 模型以保持向后兼容
class WayBillCode(BasicFieldsModel):
    """
    面单编号表 - 保留用于向后兼容
    建议新功能使用 ShippingOrderItem 模型
    """

    waybill_code = fields.CharField(max_length=32, description="面单编号", db_index=True)
    inbound_order = fields.ForeignKeyField(
        "models.InboundOrder",
        description="入库单",
        on_delete=fields.CASCADE,
        db_constraint=False,
    )
    inbound_order_detail = fields.ForeignKeyField(
        "models.InboundOrderDetail",
        description="入库单明细",
        on_delete=fields.CASCADE,
        db_constraint=False,
        related_name="waybill_codes_set",
    )
    company = fields.ForeignKeyField(
        "models.Company",
        description="公司",
        on_delete=fields.CASCADE,
        db_constraint=False,
    )
    bind_cert_status = fields.IntField(
        description="绑定证书状态(1:未绑定, 2:已绑定)",
        default=WayBillCodeBindCertStatusEnum.UNBIND.value,
    )

    class Meta:
        table = "cert_waybill_code"
        table_description = "面单编号表(已废弃，请使用ShippingOrderItem)"
