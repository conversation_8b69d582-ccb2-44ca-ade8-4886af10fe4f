# 导入所有模型，以便在其他地方可以通过 app.models 导入
from app.models.base import BasicFieldsModel
from app.models.company_models import Company
from app.models.inbound_order_models import (
    CertTypeEnum,
    InboundOrder,
    InboundOrderDetail,
    InboundOrderStatusEnum,
    ShippingOrderItem,  # 新的统一模型
    WayBillCode,  # 保留向后兼容
)
from app.models.sequence_models import Sequence
# OrderShippingRelate and ShippingQueryService have been removed
from app.models.subscribe_models import ShippingSubscribe
from app.models.user_models import User

__all__ = [
    "BasicFieldsModel",
    "Company",
    "CertTypeEnum",
    "InboundOrder",
    "InboundOrderDetail",
    "ShippingOrderItem",  # 新的统一模型
    "WayBillCode",  # 保留向后兼容
    "InboundOrderStatusEnum",
    "Sequence",
    # OrderShippingRelate and ShippingQueryService have been removed
    "ShippingSubscribe",
    "User",
]
